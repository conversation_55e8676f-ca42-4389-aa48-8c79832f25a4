<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be a Fully Qualified Name (FQN) and cannot be changed between versions. -->
    <id>com.think1024.speedsters.ideaplugin</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>Speedsters Auto</name>

    <!-- Vendor information displayed on the Plugins Page. -->
    <vendor email="<EMAIL>" url="https://toco.teitui.com/">think1024</vendor>
    <version>${project.version}</version>

    <!-- Specify the resource bundle for internationalization -->
    <resource-bundle>messages.messages</resource-bundle>

    <description>
        <![CDATA[
    Toco Design App IntelliJ IDEA Plugin enhances your experience with Toco Design.
    ]]>
    </description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>org.jetbrains.idea.maven</depends>
    <depends>com.intellij.modules.java</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <!--
            和project文件无关
        -->
        <!-- Toco通知组 -->
        <notificationGroup id="Toco" displayType="BALLOON" key="notification.group.toco"/>
        <!-- 设置面板中的Toco选项卡 -->
        <projectConfigurable instance="com.think1024.tocodesign.ideaplugin.settings.CombinedPluginSettingsConfigurable"
                             displayName="Toco Design"
                             id="com.think1024.tocodesign.ideaplugin.settings.CombinedPluginSettingsConfigurable"/>
        <!-- 注册Toco虚拟文件用于实现标签页打开Toco页面 -->
        <fileEditorProvider implementation="com.think1024.tocodesign.ideaplugin.toco.TocoWebViewEditorProvider"/>
        <fileTypeFactory implementation="com.think1024.tocodesign.ideaplugin.toco.TocoFileTypeFactory"/>
        <!-- 新建项目中的Toco选项卡 -->
        <moduleBuilder builderClass="com.think1024.tocodesign.ideaplugin.toco.TocoNewProjectModuleBuilder"/>
        <!-- 处理ApplyCode的DiffView默认选中忽略空白字符和换行 -->
        <diff.DiffExtension implementation="com.think1024.tocodesign.ideaplugin.toco.TocoDiffExtension"/>
        <!-- 处理Toco标签的正在编辑状态底色 -->
        <editorTabColorProvider implementation="com.think1024.tocodesign.ideaplugin.toco.TocoFileEditorTabColorProvider"/>
        <!-- Hook关闭Toco标签的快捷键，使其能够判断是否在编辑状态 -->
        <actionPromoter order="last" implementation="com.think1024.tocodesign.ideaplugin.actions.TocoActionPromoter"/>
        <!-- 存在编辑状态的Toco标签时关闭项目进行提示 -->
        <projectCloseHandler implementation="com.think1024.tocodesign.ideaplugin.toco.TocoProjectCloseHandler"/>
        <!-- 存储历史打开的tab页，在ProjectActivity中 -->
        <projectService serviceImplementation="com.think1024.tocodesign.ideaplugin.toco.TocoWebViewLauncherState"/>
        <runConfigurationExtension implementation="com.think1024.tocodesign.ideaplugin.env.TocoRunConfigurationExtension"/>

        <!--
            和project文件有关
        -->
        <!-- Toco toolWindow，id必须和`WindowIds`保持一致 -->
        <toolWindow id="TocoDesign"
                    anchor="right"
                    icon="/icons/toco.svg"
                    factoryClass="com.think1024.tocodesign.ideaplugin.toolWindow.TocoPluginToolWindowFactory"/>
        <toolWindow id="TocoMenu"
                    anchor="left"
                    icon="/icons/toco.svg"
                    factoryClass="com.think1024.tocodesign.ideaplugin.toolWindow.TocoPluginToolWindowFactory"
                    secondary="true"/>
        <toolWindow id="Toco Build"
                    anchor="bottom"
                    icon="/icons/toco.svg"
                    factoryClass="com.think1024.tocodesign.ideaplugin.toolWindow.TocoPluginToolWindowFactory"/>

        <!-- 左下状态栏Toco图标按钮 -->
        <statusBarWidgetFactory implementation="com.think1024.tocodesign.ideaplugin.ui.TocoDesignStatusBarWidgetFactory"
                                id="TocoDesignStatusBarWidget"/>
        <!-- java文件中的类和方法行上的Toco标记，用于跳转到对应Toco设计对象 -->
        <codeInsight.lineMarkerProvider
                language="JAVA"
                implementationClass="com.think1024.tocodesign.ideaplugin.services.locator.LocatorLineMarkerProvider"/>
        <!-- Toco相关的启动入口 -->
        <postStartupActivity implementation="com.think1024.tocodesign.ideaplugin.startup.ProjectOpenStartupActivity"/>
        <postStartupActivity implementation="com.think1024.tocodesign.ideaplugin.startup.SelectionStartupActivity"/>
        <postStartupActivity implementation="com.think1024.tocodesign.ideaplugin.startup.WorkspaceStartupActivity"/>
        <!-- CodeFileService测试启动活动 -->
        <postStartupActivity implementation="com.think1024.tocodesign.ideaplugin.startup.CodeFileServiceTestActivity"/>
        <!-- 双击shift搜索的Toco标签和搜索结果 -->
        <searchEverywhereContributor
                implementation="com.think1024.tocodesign.ideaplugin.search.TocoSearchEverywhereContributor$Factory"
                contributorFor="toco"
                displayName="Toco"
                order="after all"
        />
        <!-- 使用Toco服务的自动补全 -->
        <inline.completion.provider id="Toco" implementation="com.think1024.tocodesign.ideaplugin.completion.TocoInlineCompletionProvider"/>
    </extensions>

    <!-- Define custom actions for the plugin -->
    <actions>
        <!-- Codebase相关调试菜单，在顶部菜单的“工具”中，上线前应去除 -->
<!--        <group id="Toco.ToolsMenu" text="Toco" description="Toco Tools Menu">-->
<!--            <add-to-group group-id="ToolsMenu" anchor="last"/>-->
<!--            <action id="Toco.Search"-->
<!--                    class="com.think1024.tocodesign.ideaplugin.actions.SearchAction"-->
<!--                    text="Search"-->
<!--                    description="Search in codebase"/>-->
<!--        </group>-->
        <!-- ToolWindow页面调试工具按钮，上线前应去除 -->
        <action id="toco.openDevTool.TocoMenu"
                icon="AllIcons.Xml.Browsers.Chrome"
                class="com.think1024.tocodesign.ideaplugin.actions.devtools.TocoMenuDevToolAction"
                text="Open TocoMenu DevTool"
                description="Open DevTool for TocoMenu window"
                use-shortcut-of="GotoLine"
        />
        <action id="toco.openDevTool.TocoDesign"
                icon="AllIcons.Xml.Browsers.Chrome"
                class="com.think1024.tocodesign.ideaplugin.actions.devtools.TocoDesignDevToolAction"
                text="Open TocoDesign DevTool"
                description="Open DevTool for TocoDesign window"
                use-shortcut-of="GotoLine"
        />
        <action id="toco.openDevTool.TocoBuild"
                icon="AllIcons.Xml.Browsers.Chrome"
                class="com.think1024.tocodesign.ideaplugin.actions.devtools.TocoBuildDevToolAction"
                text="Open Build DevTool"
                description="Open DevTool for Toco Build window"
                use-shortcut-of="GotoLine"
        />
        <!-- 项目根目录右键的Toco调试菜单， 上线前应去除 -->
        <group id="Toco.ActionGroup"
               class="com.think1024.tocodesign.ideaplugin.actions.TocoActionGroup"
               text="TocoDesign"
               description="TocoDesign"
               icon="/icons/toco_color.svg">
            <add-to-group group-id="ProjectViewPopupMenu" anchor="last"/>
        </group>


        <!-- Hook关闭标签快捷键，用以处理关闭Toco标签的正在编辑判断 -->
        <action id="toco.closeContent" class="com.think1024.tocodesign.ideaplugin.actions.HookShortCutCloseAction" use-shortcut-of="CloseContent"/>

        <!-- ToolWindow页面刷新按钮 -->
        <action id="toco.refresh.TocoMenu"
                icon="AllIcons.Actions.Refresh"
                class="com.think1024.tocodesign.ideaplugin.actions.refresh.TocoMenuRefreshAction"
                text="刷新 TocoMenu"
                description="刷新 TocoMenu 窗口内容"
                use-shortcut-of="Refresh"
        >
        </action>
        <action id="toco.refresh.TocoDesign"
                icon="AllIcons.Actions.Refresh"
                class="com.think1024.tocodesign.ideaplugin.actions.refresh.TocoDesignRefreshAction"
                text="刷新 TocoDesign"
                description="刷新 TocoDesign 窗口内容"
                use-shortcut-of="Refresh"
        >
        </action>
        <action id="toco.refresh.TocoBuild"
                icon="AllIcons.Actions.Refresh"
                class="com.think1024.tocodesign.ideaplugin.actions.refresh.TocoBuildRefreshAction"
                text="刷新 Toco Build"
                description="刷新 Toco Build 窗口内容"
                use-shortcut-of="Refresh"
        >
        </action>

        <!-- ToolWindow标签栏按钮 -->
        <group id="TocoMenu.TitleBarActions">
            <reference ref="toco.refresh.TocoMenu"/>
            <!-- 上线前应去除 -->
            <reference ref="toco.openDevTool.TocoMenu"/>
        </group>
        <group id="TocoDesign.TitleBarActions">
            <reference ref="toco.refresh.TocoDesign"/>
            <!-- 上线前应去除 -->
            <reference ref="toco.openDevTool.TocoDesign"/>
        </group>
        <group id="TocoBuild.TitleBarActions">
            <reference ref="toco.refresh.TocoBuild"/>
            <!-- 上线前应去除 -->
            <reference ref="toco.openDevTool.TocoBuild"/>
        </group>

        <!-- 定义主工具栏组 -->
        <group id="Toco.MainToolbarGroup"
               class="com.think1024.tocodesign.ideaplugin.actions.toolbar.TocoMainToolbarGroup"
               text="Toco Design Tools"
               description="Toco Design toolbar actions"
               popup="false"
        >
            <!-- 将现有的按钮添加到组中 -->
            <!-- <reference ref="actionId"/> -->
            <!-- 添加到主工具栏和导航栏 -->
            <add-to-group group-id="MainToolBar" anchor="before" relative-to-action="SearchEverywhere"/>
            <add-to-group group-id="NavBarToolBar" anchor="before" relative-to-action="SearchEverywhere"/>
        </group>


        <!-- 定义顶部分隔符 Action -->
        <action id="Toco.TopSeparator" class="com.intellij.openapi.actionSystem.Separator">
            <add-to-group group-id="EditorTabPopupMenu" anchor="before" relative-to-action="Toco.WebView.Refresh"/>
        </action>

        <!-- TocoWebView 刷新 Action -->
        <action id="Toco.WebView.Refresh"
                class="com.think1024.tocodesign.ideaplugin.actions.refresh.TocoWebViewRefreshAction"
                icon="AllIcons.Actions.Refresh"
                text="Refresh Toco WebView"
                description="Refresh Toco WebView page"
                use-shortcut-of="Refresh"
        >
            <add-to-group group-id="EditorTabPopupMenu" anchor="before" relative-to-action="Toco.WebView.OpenDevTools"/>
        </action>

        <!-- TocoWebView 开发者工具 Action -->
        <action id="Toco.WebView.OpenDevTools"
                class="com.think1024.tocodesign.ideaplugin.actions.devtools.TocoWebViewDevToolAction"
                icon="AllIcons.Xml.Browsers.Chrome"
                text="Open Dev Tools"
                description="Open Toco WebView dev tools"
                use-shortcut-of="GotoLine"
        >
            <add-to-group group-id="EditorTabPopupMenu" anchor="before" relative-to-action="Toco.CopyFileUrl"/>
        </action>

        <!-- 复制 Toco 文件链接 Action -->
        <action id="Toco.CopyFileUrl"
                icon="/icons/toco_color.svg"
                class="com.think1024.tocodesign.ideaplugin.actions.CopyFileUrlAction"
                text="Copy Toco Url"
                description="Copy Toco Url to pasteboard"
        >
            <add-to-group group-id="EditorTabPopupMenu" anchor="last"/>
        </action>

        <!-- 定义底部分隔符 Action -->
        <action id="Toco.BottomSeparator" class="com.intellij.openapi.actionSystem.Separator">
            <add-to-group group-id="EditorTabPopupMenu" anchor="after" relative-to-action="Toco.CopyFileUrl"/>
        </action>
    </actions>

    <!-- Register project-level listeners -->
    <projectListeners>
        <listener class="com.think1024.tocodesign.ideaplugin.listeners.ProjectCloseListener"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </projectListeners>

    <applicationListeners>
        <!--
        Define an application-level listener for IntelliJ IDEA plugin events.
        This listener responds to plugin unload events.

        Attributes:
        - class: Specifies the fully qualified name of the listener class to be used.
        - topic: Identifies the event topic that the listener will respond to.

        In this case, the listener class `PluginUnloadListener` will handle events
        related to the `DynamicPluginListener` topic, which is typically used for
        dynamic plugin load/unload operations.
        -->

        <listener class="com.think1024.tocodesign.ideaplugin.listeners.PluginUnloadListener"
                  topic="com.intellij.ide.plugins.DynamicPluginListener"/>
    </applicationListeners>

    <depends optional="true" config-file="k2-compatibility.xml">org.jetbrains.kotlin</depends>

    <!-- Define the range of IDE versions this plugin is compatible with -->
    <idea-version since-build="243.0" until-build="252.*"/>
</idea-plugin>
