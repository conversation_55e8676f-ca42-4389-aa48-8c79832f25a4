package com.think1024.tocodesign.ideaplugin.mastery.actions.model;

import com.think1024.tocodesign.ideaplugin.mastery.tree.Tree;
// import com.think1024.tocodesign.ideaplugin.mastery.tree.TreeContext;

public abstract class Action {

    protected Tree node;

    public Action(Tree node) {
        this.node = node;
    }

    public Tree getNode() {
        return node;
    }

    public void setNode(Tree node) {
        this.node = node;
    }

    public abstract String getName();

    @Override
    public abstract String toString();
}
