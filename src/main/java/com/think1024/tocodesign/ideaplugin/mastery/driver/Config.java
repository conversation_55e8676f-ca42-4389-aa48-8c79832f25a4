package com.think1024.tocodesign.ideaplugin.mastery.driver;

import java.util.*;

public final class Config {
    // "ending with 'Sim'" means the type is double,
    // otherwise, the type is assumed to be int
    public enum Hyperparameter {
        minHeight,
        sepSize,
        minCodeSim,
        minDiceSim,
        maxSize,
        stopCodeSim
    }

    // common arguments
    public String language = "JAVA";

    /* ------- Merge Mode -------*/
    public String base;
    public String left;
    public String right;

    // formatter
    public String formatter = null;

    // algorithm
    public String algorithm = "SKINCHANGER";

    // hyperparameters
    public Map<Hyperparameter, Object> hyperparameters = new HashMap<Hyperparameter, Object>();

    public ParserConfig parserConfig = new ParserConfig(false);

    public Config(String left, String base, String right) {
        this.left = left;
        this.base = base;
        this.right = right;
    }

    public static final class ParserConfig {
        public boolean keepComment;

        public ParserConfig(boolean keepComment) {
            this.keepComment = keepComment;
        }
    }
}
