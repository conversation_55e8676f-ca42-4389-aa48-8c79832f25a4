package com.think1024.tocodesign.ideaplugin.mastery.actions.model;

import com.think1024.tocodesign.ideaplugin.mastery.tree.Tree;

public class Insert extends Addition {

    public Insert(Tree node, Tree parent, int pos) {
        super(node, parent, pos);
    }

    @Override
    public String getName() {
        return "INS";
    }

    @Override
    public String toString() {
        if (node.parent == null) return getName() + " root " + node;
        else return getName() + " " + node + " into " + parent + " at " + pos;
    }
}
