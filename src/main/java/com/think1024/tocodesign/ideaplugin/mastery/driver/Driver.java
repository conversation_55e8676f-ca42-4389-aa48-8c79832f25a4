package com.think1024.tocodesign.ideaplugin.mastery.driver;

import java.util.*;

import com.think1024.tocodesign.ideaplugin.mastery.matcher.MatchingSet;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.ThreeWayMatcher;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.TwoWayMatcher;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.changedistiller.ChangeDistillerTwoWayMatcher;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.gumtree.GumTreeTwoWayMatcher;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.skinchanger.SkinChangerTwoWayMatcher;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.jdime.JDimeTwoWayMatcher;
import com.think1024.tocodesign.ideaplugin.mastery.matcher.Assigner;
import com.think1024.tocodesign.ideaplugin.mastery.merger.Merger;
import com.think1024.tocodesign.ideaplugin.mastery.merger.TopDownPruningMerger;
import com.think1024.tocodesign.ideaplugin.mastery.tree.Tree;
import com.think1024.tocodesign.ideaplugin.mastery.tree.TreeBuilders;
import com.think1024.tocodesign.ideaplugin.mastery.tree.TreePrinters;
import org.jetbrains.annotations.Nullable;

public final class Driver {
    public static @Nullable String merge(Config config) {
        try {
            // Parse AST from source code
            Tree left = TreeBuilders.fromSource(config.left, config.parserConfig);
            Tree base = TreeBuilders.fromSource(config.base, config.parserConfig);
            Tree right = TreeBuilders.fromSource(config.right, config.parserConfig);

            // Phase I: Assign
            Assigner assigner = new Assigner();
            assigner.apply(base, left, right);

            // Phase II: Mapping
            TwoWayMatcher twoWayMatcher = getTwoWayMatcherFromAlgorithm(config.algorithm, config.hyperparameters);
            ThreeWayMatcher threeWayMatcher = new ThreeWayMatcher(twoWayMatcher);
            MatchingSet mappings = threeWayMatcher.apply(base, left, right);

            // Phase III: Merge
            Merger merger = new TopDownPruningMerger();
            Tree target = merger.apply(mappings);

            boolean hasConflict = false;
            for (Tree node : target.preOrder()) {
                if (node.isConflict()) {
                    hasConflict = true;
                    break;
                }
            }

            if (hasConflict) {
                return null;
            }
            return TreePrinters.prettyCode(target, config.left, config.right, config.formatter, config.language);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static TwoWayMatcher getTwoWayMatcherFromAlgorithm(String algorithm, Map<Config.Hyperparameter, Object> hyperparameters) {
        switch (algorithm) {
            case "GUMTREE":
                return new GumTreeTwoWayMatcher();
            case "SKINCHANGER":
                return new SkinChangerTwoWayMatcher(hyperparameters);
            case "JDIME":
                return new JDimeTwoWayMatcher(false);
            case "JDIME-LOOKAHEAD":
                return new JDimeTwoWayMatcher(true);
            default:
                assert (algorithm.equals("CHANGEDISTILLER"));
                return new ChangeDistillerTwoWayMatcher();
        }
    }
}
