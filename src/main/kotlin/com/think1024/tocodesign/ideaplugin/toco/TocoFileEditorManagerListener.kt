package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.messages.MessageBusConnection
import com.think1024.tocodesign.ideaplugin.actions.hookCloseTab
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.utils.getQueryParams
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowserManager
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.net.URL

@OptIn(DelicateCoroutinesApi::class)
class TocoFileEditorManagerListener(private val project: Project) {
    
    private var connection: MessageBusConnection? = null
    
    init {
        // 注册监听器
        connection = project.messageBus.connect()
        connection?.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
            override fun fileOpened(source: FileEditorManager, file: VirtualFile) {
                if (file is TocoVirtualFile) {
                    // 获取文件类型
                    val type = file.getUserData(TocoVirtualFile.TYPE_KEY) ?: "java"
                    
                    // 获取对应的图标
                    val icon = TocoWebViewLauncher.iconMap[type] ?: file.getDisplayIcon()
                    
                    // 设置文件图标
                    file.putUserData(TocoVirtualFile.ICON_KEY, icon)
                    
                    // 更新文件表示
                    source.updateFilePresentation(file)
                    
                    println("✅ 文件打开事件: ${file.name}, 类型: $type")

                    hookCloseTab(project, file)
                }
            }

            override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
                // 只清理我们插件中打开的特殊文件，比如 .toco 文件
                if (file is TocoVirtualFile) {
                    val url = file.webUrl
                    println("Closed TocoVirtualFile, url = $url")
                    TocoWebViewLauncher.clearFile(url)

                    // 从持久化状态中移除
                    TocoWebViewLauncherState.getInstance(project).removeOpenedFile(url)

                    // 如果是modeling_apply关闭需要往聊天窗发送消息
                    if (url.contains("modeling_apply")) {
                        val urlObj = URL(url)
                        val searchParams = urlObj.getQueryParams()
                        val threadId = searchParams["threadId"] ?: ""
                        // 发送到chat页面
                        val chatBrowser = TocoBrowserManager.getInstance(project).getBrowser(WindowIds.TOCO_DESIGN)
                        GlobalScope.launch {
                            chatBrowser.getBrowser().sendToWebview("modeling-apply-$threadId")
                        }
                    }
                } else {
                    println("Closed non-TocoVirtualFile: ${file.path}")
                }
            }
            
            override fun selectionChanged(event: FileEditorManagerEvent) {
                // 当选择改变时，确保图标正确
                val newFile = event.newFile
                if (newFile is TocoVirtualFile) {
                    // 获取文件类型
                    val type = newFile.getUserData(TocoVirtualFile.TYPE_KEY) ?: "java"
                    val url = newFile.webUrl
                    val title = newFile.name

                    // 获取对应的图标
                    val icon = TocoWebViewLauncher.iconMap[type] ?: newFile.getDisplayIcon()
                    // 设置文件图标
                    newFile.putUserData(TocoVirtualFile.ICON_KEY, icon)
                    // 更新文件表示
                    event.manager.updateFilePresentation(newFile)

                    // 使用优化后的方法发送标签页激活消息
                    WebViewBridge.sendTabActivated(
                        project,
                        WindowIds.TOCO_MENU,
                        url,
                        title,
                        type
                    )
                    println("✅ 发送标签页激活消息: $url")
                }
            }
        })
    }
    
    fun dispose() {
        connection?.disconnect()
        connection = null
    }

    /**
     * 检查当前打开的标签页
     */
    fun checkCurrentTab() {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val selectedFiles = fileEditorManager.selectedFiles

        if (selectedFiles.isNotEmpty()) {
            val currentFile = selectedFiles[0]
            if (currentFile is TocoVirtualFile) {
                val url = currentFile.webUrl
                val title = currentFile.name
                val type = currentFile.getUserData(TocoVirtualFile.TYPE_KEY) ?: "unknown"

                // 发送标签页激活消息
                WebViewBridge.sendTabActivated(
                    project,
                    "TocoDesign",
                    url,
                    title,
                    type
                )
                println("✅ 初始化检查 - 发送标签页激活消息: $url")
            }
        }
    }

    companion object {
        fun getInstance(project: Project): TocoFileEditorManagerListener {
            return TocoFileEditorManagerListener(project)
        }
    }
}