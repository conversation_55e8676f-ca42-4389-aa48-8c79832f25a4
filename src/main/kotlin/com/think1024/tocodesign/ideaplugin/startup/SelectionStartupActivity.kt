package com.think1024.tocodesign.ideaplugin.startup

import com.intellij.openapi.project.Project
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiJavaFile
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class SelectionStartupActivity : TocoProjectActivity() {
    @OptIn(DelicateCoroutinesApi::class)
    override fun runActivity(project: Project) {
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return
        }
        val multicaster = EditorFactory.getInstance().eventMulticaster

        multicaster.addSelectionListener(object : SelectionListener {
            override fun selectionChanged(e: SelectionEvent) {
                val editor = e.editor
                val selectedText = editor.selectionModel.selectedText ?: return

                // 检查选中内容是否为空或仅包含空白字符
                if (selectedText.trim().isEmpty()) {
                    return
                }

                // 检查编辑器所属文件类型和上下文
                val project = editor.project ?: return
                val editorFile = FileDocumentManager.getInstance().getFile(editor.document)

                // 排除非项目文件或特殊编辑器
                if (editorFile == null || !editorFile.isValid || !editorFile.isInLocalFileSystem) {
                    return  // 排除虚拟文件或非本地文件系统的文件
                }

                val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(editor.document)
                if (psiFile !== null) {
                    println("✅ 当前选中区域: $selectedText")

                    // 获取文件信息
                    val fileInfo = createFileInfo(editorFile, selectedText, project)

                    // 启动协程，异步向 WebView 发送消息
                    GlobalScope.launch {
                        try {
                            WebViewBridge.sendIfReady(project, "TocoDesign", "selection-changed", fileInfo)
                        } catch (e: Exception) {
                            println("❌ WebView 请求失败: ${e.message}")
                        }
                    }
                }
            }
        }, project)

        println("✅ SelectionListener 已注册")
    }

    /**
     * 创建包含文件信息的Map
     */
    private fun createFileInfo(file: VirtualFile, content: String, project: Project): Map<String, String> {
        val projectRelativePath = getProjectRelativePath(project, file)
        return mapOf(
            "name" to file.name,
            "path" to projectRelativePath,
            "content" to content
        )
    }

    private fun getProjectRelativePath(project: Project, file: VirtualFile): String {
        val fullPath = file.path
        val projectName = project.name
        val projectNameWithSeparator = "/$projectName/"

        val projectNameIndex = fullPath.indexOf(projectNameWithSeparator)

        if (projectNameIndex != -1) {
            val relativePath = fullPath.substring(projectNameIndex + projectNameWithSeparator.length)
            return relativePath;
        } else {
            println("无法在路径中找到项目名称: $projectName，路径: $fullPath")
            return fullPath
        }
    }
}