package com.think1024.tocodesign.ideaplugin.services

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffDialogHints
import com.intellij.diff.DiffManager
import com.intellij.diff.chains.SimpleDiffRequestChain
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.util.DiffUserDataKeysEx
import com.intellij.ide.actions.SynchronizeCurrentFileAction
import com.intellij.notification.Notification
import com.intellij.notification.NotificationAction
import com.intellij.notification.NotificationType
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.ui.Messages
import com.think1024.tocodesign.ideaplugin.actions.ApplyAllChangesAction
import com.think1024.tocodesign.ideaplugin.actions.RejectAllChangesAction
import com.think1024.tocodesign.ideaplugin.services.CodeGenerationService.GenerateCodeResultType
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.*
import com.think1024.tocodesign.ideaplugin.toco.Fossil.getModuleBranchFolder
import com.think1024.tocodesign.ideaplugin.toolWindow.TocoDiffFile
import com.think1024.tocodesign.ideaplugin.toolWindow.TocoMergeResultDiffDialog
import com.think1024.tocodesign.ideaplugin.toolWindow.TocoMultipleFileMergeDialog
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.JacksonUtil.toList
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.ProgressIndicator
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowser
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONArray

@OptIn(DelicateCoroutinesApi::class)
fun notifyGenerateResult(
    eventType: String,
    browser: TocoBrowser,
    moduleName: String,
    result: CodeGenerateFossilResult,
    resultType: GenerateCodeResultType,
    recover: Boolean,
    autoConflictResolved: List<TocoDiffFile>,
    vararg actions: NotificationAction?
) {
    var message: String? = ""
    var notificationType: NotificationType? = null
    var title: String = getI18nString("code.generate.success", moduleName)
    var content = ""
    when(resultType) {
        GenerateCodeResultType.CANCELLED -> {
            result.abort?.invoke()
            message = "Cancelled"
            notificationType = NotificationType.INFORMATION
            title = getI18nString("code.generate.cancelled", moduleName)
        }
        GenerateCodeResultType.FINISHED -> {
            val res = result.finish?.invoke(recover)
            if (res?.success == true) {
                message = ""
                notificationType = NotificationType.INFORMATION
            } else {
                message = res?.message
                content = result.message ?: ""
                notificationType = NotificationType.ERROR
            }
        }
        GenerateCodeResultType.FAILED -> {
            // 这里不需要调用result的invoke
            message = result.message
            content = result.message ?: ""
            notificationType = NotificationType.ERROR
            title = getI18nString("code.generate.failed", moduleName)
        }
    }

    GlobalScope.launch {
        NotificationService.instance.notify(
            title,
            content,
            type = notificationType,
            actions = actions.filterNotNull().toTypedArray(),
        )
        browser.sendToWebview(eventType, mapOf(
            "message" to message,
            "autoConflictResolved" to autoConflictResolved,
        ))
        System.gc()
    }
}

class RecoverFilesAction(
    private val result: CodeGenerateFossilResult,
): NotificationAction(getI18nString("code.generate.resolve.recover")) {
    override fun actionPerformed(action: AnActionEvent, nofitiation: Notification) {
        val fileMergeDialog = action.project?.let {
            TocoMultipleFileMergeDialog(
                it,
                (result.data ?: listOf()).filter { file -> file.type == MergeStatus.IGNORED },
            ) {
                if (!it && action.project?.basePath != null) {
                    // 因为此时生成代码流程已结束，需要单独提交恢复的文件
                    FossilCmd.commitFiles(action.project?.basePath!!, "recover files", false)
                }
            }
        }
        fileMergeDialog?.show()
    }
}

class TocoMergeResultDebugAction(
    private val conflictIgnoreFiles: List<TocoDiffFile>,
): NotificationAction(getI18nString("code.generate.resolve.merge.diff.confirm", conflictIgnoreFiles.size.toString())) {
    override fun actionPerformed(action: AnActionEvent, nofitiation: Notification) {
        val fileMergeDialog = action.project?.let {
            TocoMergeResultDiffDialog(
                it,
                conflictIgnoreFiles,
            )
        }
        fileMergeDialog?.show()
    }
}

class DotShowRecoverFile(private val moduleId: String, private val project: Project): DialogWrapper.DoNotAskOption.Adapter() {
    override fun rememberChoice(isSelected: Boolean, exitCode: Int) {
        if (exitCode == Messages.OK) {
            ProjectPluginSettings.getInstance(project).setShouldShowIgnoreFileModal(moduleId, !isSelected)
        }
    }

    override fun getDoNotShowMessage(): String {
        return getI18nString("code.generate.recover.do.not.show")
    }
}

@Service(Service.Level.PROJECT)
class CodeGenerationService(private val project: Project) {
    private val generateIndicator: ProgressIndicator = ProgressIndicator()
    private val notificationService = NotificationService.instance

    private fun reloadProject() {
        if (project.basePath.isNullOrEmpty()) {
            return
        }
        val projectRoot = FileUtil.getVirtualFile(project.basePath!!)
        if (projectRoot == null) {
            return
        }
        ApplicationManager.getApplication().executeOnPooledThread {
            SynchronizeCurrentFileAction.synchronizeFiles(listOf(projectRoot), project, false)
        }
    }

    /**
     * 创建模块
     */
    fun createModule(projectItem: Item, moduleItem: Item, config: BoilerplateCmd.GenerateModuleConfig): FossilResult {
        return Fossil.createModule(
            projectItem, moduleItem, project.basePath!!,
            onRenderModule = { _, _, modulePath ->
                val res = BoilerplateCmd.generateModule(config, modulePath)
                FossilResult(res.exitCode == 0 || (res.error ?: "").contains("Module already exists"))
            },
            onSuccess = {
                reloadProject()
            },
        )
    }

    /**
     * 生成代码
     */
    private fun generateCode(config: JsonObject): CodeGenerateFossilResult {
        val projectItem = Item(config["projectId"].asString, config["projectName"].asString)
        val moduleItem = Item(config["moduleId"].asString, config["moduleName"].asString)
        val groupId = config["groupId"].asString
        var autoConflictResolve = false

        if (ApplicationPluginSettings.getInstance().getIsDev()) {
            autoConflictResolve = true
        } else {
            autoConflictResolve = ApplicationPluginSettings.getInstance().autoConflictResolve
        }

        // 先检查模块是否存在
        val moduleFolder = getModuleBranchFolder(project.name, moduleItem.name)
        var moduleInfo = ProjectConfigUtil.getModuleInfo(Fossil.getModuleWorkFolder(moduleFolder, moduleItem.name))
        val projectModuleFilePath = Fossil.getModuleWorkFolder(project.basePath!!, moduleItem.name)
        val projectModuleFile = FileUtil.getFile(projectModuleFilePath)
        if (projectModuleFile == null) {
            // 如果本地没有，则删除分支重新生成
            generateIndicator.setText(getI18nString("code.generate.clean.module"))
            Fossil.cleanModule(projectItem, project.basePath!!, moduleItem.name)
            moduleInfo = null
        }
        val projectModuleInfo = ProjectConfigUtil.getModuleInfo(projectModuleFilePath)
        // 本地有模块但没分支，则认为是已有代码新建fossil工程，所有冲突以本地的代码为准
        val ignoreConflict = moduleInfo == null && projectModuleInfo != null
        if (moduleInfo == null) {
            generateIndicator.setText(getI18nString("code.generate.module.config"))
            // 如果不存在，则先生成模块
            // 请求模块配置
            val (configs, message) = TocoService.get(
                "url.path.create.module.config",
                mapOf("type" to "restore"),
                emptyMap(),
                30
            )
            if (configs == null || configs !is JSONArray || configs.length() == 0) {
                return CodeGenerateFossilResult(false, message)
            }
            
            // 找到第一个可用的配置
            val moduleConfig = configs.toList().find { config ->
                config is LinkedHashMap<*, *> &&
                        (config as LinkedHashMap<String, *>).getValue("enabled") == true
            } as LinkedHashMap<String, *>?
            
            if (moduleConfig != null) {
                generateIndicator.setText(getI18nString("code.generate.create.module"))
                // 生成模块
                val ret = createModule(
                    projectItem, moduleItem, BoilerplateCmd.GenerateModuleConfig(
                        url = moduleConfig.getValue("url") as String,
                        ref = moduleConfig.getValue("ref") as String,
                        projectId = projectItem.uuid,
                        projectName = projectItem.name,
                        groupId = groupId,
                        moduleName = moduleItem.name,
                        moduleId = moduleItem.uuid,
                    )
                )
                if (!ret.success) {
                    return CodeGenerateFossilResult(false, ret.message)
                }
            } else {
                return CodeGenerateFossilResult(false, message)
            }
        }
        generateIndicator.setFraction(0.3)
        val result = config["result"]?.asJsonArray
        val code: CodeGenerateResult?
        try {
            code = CodeGenerateResult(
                moduleId = config["moduleId"].asString,
                moduleName = config["moduleName"].asString,
                codeFileList = result?.map { json ->
                    val file = json.asJsonObject
                    CodeDescription(
                        code = file["code"].asString,
                        path = file["path"].asString,
                        fullPath = file["fullPath"].asString,
                        overwrite = file["overwrite"].asBoolean,
                        moduleName = file["moduleName"]?.asString,
                        groupName = file["groupName"]?.asString,
                        artifactName = file["artifactName"]?.asString
                    )
                }
            )
        } catch (e: Exception) {
            return CodeGenerateFossilResult(false, e.message)
        }
        generateIndicator.setText(getI18nString("code.generate.generating"))
        return Fossil.generateCode(
            projectItem, project.basePath!!, code, ignoreConflict, autoConflictResolve,
            stateCallback = { state, writeProgress, diffProgress ->
                generateIndicator.setText(state)
                generateIndicator.setFraction(0.3 + 0.5 * writeProgress + 0.19 * diffProgress)
            },
            onSuccess = {
                reloadProject()
            },
        )
    }

    enum class GenerateCodeResultType {
        CANCELLED,
        FINISHED,
        FAILED,
    }

    fun areStringsEqualIgnoreWhitespace(str1: String, str2: String): Boolean {
        val normalizedStr1 = str1.replace("\\s".toRegex(), "")
        val normalizedStr2 = str2.replace("\\s".toRegex(), "")
        return normalizedStr1 == normalizedStr2
    }

    /**
     * 处理生成代码事件
     */
    fun handleGenerateCodeEvent(browser: TocoBrowser, config: JsonObject) {
        System.gc()
        val id = config["id"].asString
        val moduleId = config["moduleId"].asString
        val moduleName = config["moduleName"].asString

        generateIndicator.setFraction(0.1)
        val result = generateCode(config)
        // 筛选出所有有冲突但被自动解决的文件
        val conflictResolvedFiles = (result.data?.filter { it.conflictResolved == true } ?: emptyList()).map {
            TocoDiffFile(
                path = it.mergedPath,
                oursContent = it.oursContent,
                theirsContent = it.theirsContent,
                mergedContent = it.mergedContent,
            )
        }
        val conflictResolvedAction = if (
            (ApplicationPluginSettings.getInstance().autoConflictResolveDebug || ApplicationPluginSettings.getInstance().getIsDev()) &&
            conflictResolvedFiles.isNotEmpty()
            )
        {
            TocoMergeResultDebugAction(conflictResolvedFiles)
        } else {
            null
        }

        // 筛选出所有有修改的自动解决文件交给前端显示
        val retConflictResolvedFiles = conflictResolvedFiles.filter { !areStringsEqualIgnoreWhitespace(it.oursContent, it.mergedContent) }
        if (result.success) {
            val conflictFile = result.data?.find { it.type == MergeStatus.UNMERGED }
            if (conflictFile != null) {
                // 有冲突：解决
                ApplicationManager.getApplication().invokeLater {
                    try {
                        val fileMergeDialog = TocoMultipleFileMergeDialog(
                            project,
                            result.data.filter { file -> file.type == MergeStatus.UNMERGED || file.type == MergeStatus.IGNORED },
                        ) { abort ->
                            notifyGenerateResult(
                                "generate-code:$id",
                                browser,
                                moduleName,
                                result,
                                if (abort) GenerateCodeResultType.CANCELLED else GenerateCodeResultType.FINISHED,
                                false,
                                retConflictResolvedFiles,
                                conflictResolvedAction
                            )
                        }
                        fileMergeDialog.show()
                    } catch (e: Exception) {
                        e.printStackTrace()
                        notifyGenerateResult(
                            "generate-code:$id",
                            browser,
                            moduleName,
                            result,
                            GenerateCodeResultType.CANCELLED,
                            false,
                            retConflictResolvedFiles,
                            conflictResolvedAction
                        )
                    }
                }
            } else {
                val ignoredFile = result.data?.find { it.type == MergeStatus.IGNORED }
                if (ignoredFile != null) {
                    // 有需要恢复的文件，进行询问
                    val shouldModule = ProjectPluginSettings.getInstance(project).shouldShowIgnoreFileModal(moduleId)
                    if (shouldModule) {
                        ApplicationManager.getApplication().invokeLater {
                            val reply = Messages.showOkCancelDialog(project,
                                getI18nString("code.generate.recover.message"),
                                getI18nString("code.generate.recover.title"),
                                getI18nString("code.generate.resolve.recover.button"),
                                getI18nString("button.cancel"),
                                Messages.getWarningIcon(),
                                DotShowRecoverFile(moduleId, project),
                            )
                            if (reply == Messages.OK) {
                                val fileMergeDialog = TocoMultipleFileMergeDialog(
                                    project,
                                    result.data.filter { file -> file.type == MergeStatus.IGNORED },
                                ) {
                                    if (it) {
                                        // 取消时清理掉已写入的文件
                                        FossilCmd.clean(project.basePath!!)
                                    }
                                    notifyGenerateResult(
                                        "generate-code:$id",
                                        browser,
                                        moduleName,
                                        result,
                                        GenerateCodeResultType.FINISHED,
                                        true,
                                        retConflictResolvedFiles,
                                        conflictResolvedAction
                                    )
                                }
                                fileMergeDialog.show()
                            } else {
                                notifyGenerateResult(
                                    "generate-code:$id",
                                    browser,
                                    moduleName,
                                    result,
                                    GenerateCodeResultType.FINISHED,
                                    false,
                                    retConflictResolvedFiles,
                                    conflictResolvedAction
                                )
                            }
                        }
                    } else {
                        notifyGenerateResult(
                            "generate-code:$id",
                            browser,
                            moduleName,
                            result,
                            GenerateCodeResultType.FINISHED,
                            false,
                            retConflictResolvedFiles,
                            RecoverFilesAction(result),
                            conflictResolvedAction
                        )
                    }
                } else {
                    // 没冲突也没恢复：提交
                    notifyGenerateResult(
                        "generate-code:$id",
                        browser,
                        moduleName,
                        result,
                        GenerateCodeResultType.FINISHED,
                        false,
                        retConflictResolvedFiles,
                        conflictResolvedAction
                    )
//                if (!result.data.isNullOrEmpty()) {
//                    val file = result.data[0]
//                    handleApplyCode(browser, JsonObject().apply {
//                        addProperty("path", file.mergedPath)
//                        addProperty("code", file.mergedContent)
//                    })
//                    notifyGenerateResult("generate-code:$id", browser, result, GenerateCodeResultType.CANCELLED, false, retConflictResolvedFiles, conflictResolvedAction)
//                } else {
//
//                    notifyGenerateResult("generate-code:$id", browser, result, GenerateCodeResultType.FINISHED, false, retConflictResolvedFiles, conflictResolvedAction)
//                }
                }
            }
        } else {
            notifyGenerateResult(
                "generate-code:$id",
                browser,
                moduleName,
                result,
                GenerateCodeResultType.FAILED,
                false,
                retConflictResolvedFiles,
                conflictResolvedAction
            )
        }
        generateIndicator.setFraction(1.0)
        generateIndicator.hide()
    }

    /**
     * 处理生成代码失败事件
     */
    fun handleGenerateCodeFailedEvent(config: JsonObject) {
        notificationService.notify(
            getI18nString("code.generate.failed2"),
            config["msg"]?.asString ?: "",
            type = NotificationType.ERROR
        )
        generateIndicator.hide()
    }

    /**
     * 显示生成代码进度指示器
     */
    fun showGenerateCodeIndicator() {
        generateIndicator.setText(getI18nString("code.generate.fetching.code"))
        generateIndicator.show(project, "${getI18nString("code.generate.start")}...", false)
    }

    fun handleApplyAllCodes(browser: TocoBrowser, files: JsonArray) {
        System.gc()
        ApplicationManager.getApplication().invokeLater({
            generateIndicator.show(project, "${getI18nString("code.apply")}...", false)
        }, ModalityState.nonModal())

        generateIndicator.setFraction(0.0)
        val count = files.size()
        ApplicationManager.getApplication().runWriteAction {
            files.forEachIndexed { index, file ->
                run {
                    val config = file.asJsonObject
                    val path = config["path"].asString
                    val code = config["code"].asString
                    val virtualFile = FileUtil.getVirtualFile(path, project)
                    println("code $code")
                    println("codeToByteArray ${code.toByteArray()}")
                    virtualFile?.setBinaryContent(code.toByteArray())
                    generateIndicator.setFraction((index + 1) * 1.0 / count)
                }
            }
        }
        ApplicationManager.getApplication().executeOnPooledThread {
            Thread.sleep(1000)
            // 执行隐藏逻辑
            generateIndicator.hide()
            notificationService.notify(
                getI18nString("code.apply.all.finished"),
                "",
                project,
                NotificationType.INFORMATION
            )
            System.gc()
        }
    }

    fun handleApplyCode(browser: TocoBrowser, config: JsonObject) {
        val path = config["path"].asString
        val code = config["code"].asString
        val virtualFile = FileUtil.getVirtualFile(path, project)
        if (virtualFile == null) {
            notificationService.notify(
                getI18nString("code.apply.failed"),
                getI18nString("code.apply.failed.file.not.exist"),
                type = NotificationType.ERROR
            )
            return
        }
        val service = CodeFileService(project)
        val orig = service.getFileContent(virtualFile) ?: ""
        val content1 = DiffContentFactory.getInstance().create(project, virtualFile)
        val content2 = DiffContentFactory.getInstance().create(project, code, virtualFile.fileType)
        val diffRequest = SimpleDiffRequest(
            getI18nString("code.apply"),
            content1,
            content2,
            "${virtualFile.name}(${getI18nString("code.generate.ours")})",
            "${virtualFile.name}(${getI18nString("code.generate.theirs")})",
        )
        diffRequest.putUserData(TOCO_APPLY_CODE_KEY, true)
        val requestChain = SimpleDiffRequestChain(diffRequest)
        requestChain.putUserData(DiffUserDataKeysEx.DIFF_NEW_TOOLBAR, true)
        requestChain.putUserData(DiffUserDataKeysEx.DISABLE_CONTENTS_EQUALS_NOTIFICATION, true)
        requestChain.putUserData(DiffUserDataKeysEx.CONTEXT_ACTIONS, listOf(
            ApplyAllChangesAction(code, virtualFile),
            RejectAllChangesAction(orig, virtualFile)
        ))
        ApplicationManager.getApplication().invokeLater {
            DiffManager.getInstance().showDiff(project, requestChain, DiffDialogHints.DEFAULT)
        }
    }

    companion object {
        fun getInstance(project: Project): CodeGenerationService {
            return project.getService(CodeGenerationService::class.java)
        }
    }
}