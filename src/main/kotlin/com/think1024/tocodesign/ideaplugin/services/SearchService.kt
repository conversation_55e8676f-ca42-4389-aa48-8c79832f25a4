package com.think1024.tocodesign.ideaplugin.services

import com.intellij.find.FindModel
import com.intellij.usages.FindUsagesProcessPresentation
import com.intellij.find.impl.FindInProjectUtil
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.usages.UsageViewPresentation

@Service(Service.Level.PROJECT)
class SearchService(private val project: Project) {

    data class SearchResult(
        val file: VirtualFile,
        val line: Int,
        val column: Int,
        val text: String,
        val context: String
    )

    data class FindInFilesMatch(
        val filePath: String,          // 文件相对路径
        val lineNumber: Int,           // 匹配行号（1基）
        val lineContent: String,       // 匹配行的完整内容
        val matchStart: Int,           // 匹配开始位置（在行内的字符索引）
        val matchEnd: Int,             // 匹配结束位置（在行内的字符索引）
        val matchText: String          // 匹配的文本内容
    )

    data class FindInFilesResult(
        val query: String,             // 搜索查询
        val totalMatches: Int,         // 总匹配数量
        val matches: List<FindInFilesMatch> // 匹配结果列表（最多10个）
    )

    fun searchInProject(
        searchText: String,
        caseSensitive: Boolean = false,
        wholeWords: Boolean = false,
        regex: Boolean = false
    ): FindInFilesResult {

        // 检查searchText是否是正则表达式
        val isRegexEnabled = regex || searchText.contains(Regex("[\\[\\]{}()*+?.,\\\\^$|#]"))

        val findModel = FindModel().apply {
            stringToFind = searchText
            isCaseSensitive = caseSensitive
            isWholeWordsOnly = wholeWords
            isRegularExpressions = isRegexEnabled
            setSearchContext(FindModel.SearchContext.ANY)
        }

        val results = mutableListOf<SearchResult>()

        FindInProjectUtil.findUsages(
            findModel,
            project,
            { usageInfo ->
                // 检查是否已达到限制
                if (results.size >= 20) {
                    return@findUsages false // 返回 false 停止搜索
                }

                val file = usageInfo.file?.virtualFile
                if (file != null) {
                    val document = FileDocumentManager.getInstance().getDocument(file)
                    if (document != null) {
                        val offset = usageInfo.navigationOffset
                        val lineNumber = document.getLineNumber(offset)
                        val lineStartOffset = document.getLineStartOffset(lineNumber)
                        val lineEndOffset = document.getLineEndOffset(lineNumber)
                        val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))
                        val column = offset - lineStartOffset

                        results.add(SearchResult(
                            file = file,
                            line = lineNumber + 1,
                            column = column + 1,
                            text = searchText,
                            context = lineText
                        ))

                        // 添加后再次检查，如果达到限制立即停止
                        if (results.size >= 20) {
                            return@findUsages false
                        }
                    }
                }
                true
            },
            FindUsagesProcessPresentation(
                UsageViewPresentation().apply {
                    searchString = searchText
                }
            )
        )

        // 转换为 FindInFilesResult
        val matches = results.map { result ->
            val filePath = result.file.path
            val lineContent = result.context
            val searchTextLength = searchText.length
            val matchStart = result.column
            val matchEnd = matchStart + searchTextLength

            FindInFilesMatch(
                filePath = filePath,
                lineNumber = result.line,
                lineContent = lineContent,
                matchStart = matchStart,
                matchEnd = matchEnd,
                matchText = searchText
            )
        }

        // 将matches里的数量上限控制在20个
        val limitedMatches = matches.take(20)
        return FindInFilesResult(
            query = searchText,
            totalMatches = limitedMatches.size,
            matches = limitedMatches
        )
    }

    companion object {
        fun getInstance(project: Project): SearchService = project.service()
    }
}