package com.think1024.tocodesign.ideaplugin.toolWindow

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.content.ContentFactory
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.services.CodeGenerationService
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.Item
import com.think1024.tocodesign.ideaplugin.toco.BoilerplateCmd
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowserManager
import java.util.*

class TocoPluginToolWindowFactory : ToolWindowFactory, DumbAware {
  override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
    val browser = TocoBrowserManager.getInstance(project).getBrowser(toolWindow.id)
    val content = ContentFactory.getInstance().createContent(browser.getComponent(), null, false)
    toolWindow.contentManager.addContent(content)

    // 获取代码生成服务
    val codeGenerationService = CodeGenerationService.getInstance(project)

    // 添加标题栏操作
    val titleActions = mutableListOf<AnAction>()
    when (toolWindow.id) {
      WindowIds.TOCO_MENU -> {
        createTitleActions(titleActions, "TocoMenu.TitleBarActions")
      }
      WindowIds.TOCO_DESIGN -> {
        createTitleActions(titleActions, "TocoDesign.TitleBarActions")
        if (project.basePath != null) {
          // 应用代码
          browser.getBrowser().addEventListener("apply-code") { config ->
            codeGenerationService.handleApplyCode(browser.getBrowser(), config)
          }
          browser.getBrowser().addEventListener("apply-all-codes") { body ->
            codeGenerationService.handleApplyAllCodes(browser.getBrowser(), body.get("files").asJsonArray)
          }
        }
      }
      WindowIds.TOCO_BUILD -> createTitleActions(titleActions, "TocoBuild.TitleBarActions")
    }

    // 添加最大化操作
    val maximizeAction = ActionManager.getInstance().getAction("MaximizeToolWindow")
    if (maximizeAction != null) {
      titleActions.add(maximizeAction)
    }

    toolWindow.setTitleActions(titleActions)
  }

  private fun createTitleActions(titleActions: MutableList<in AnAction>, groupId: String) {
    val action = ActionManager.getInstance().getAction(groupId)
    if (action != null) {
      titleActions.add(action)
    }
  }

  override fun shouldBeAvailable(project: Project) = ProjectPluginSettings.getInstance(project).projectId != null
}